import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button, Card } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING } from '../constants/AppConstants';

const ErrorMessage = ({ 
  message = 'حدث خطأ غير متوقع', 
  onRetry = null,
  retryText = 'إعادة المحاولة',
  style,
  showIcon = true 
}) => {
  return (
    <View style={[styles.container, style]}>
      <Card style={styles.card}>
        <Card.Content style={styles.content}>
          {showIcon && (
            <MaterialIcons 
              name="error-outline" 
              size={48} 
              color={COLORS.error} 
              style={styles.icon}
            />
          )}
          
          <Text style={styles.message}>{message}</Text>
          
          {onRetry && (
            <Button 
              mode="contained" 
              onPress={onRetry}
              style={styles.retryButton}
              buttonColor={COLORS.primary}
              textColor={COLORS.surface}
            >
              {retryText}
            </Button>
          )}
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
    backgroundColor: COLORS.background,
  },
  card: {
    width: '100%',
    maxWidth: 400,
    backgroundColor: COLORS.surface,
    elevation: 2,
  },
  content: {
    alignItems: 'center',
    padding: SPACING.lg,
  },
  icon: {
    marginBottom: SPACING.md,
  },
  message: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    lineHeight: 24,
    fontFamily: 'System',
  },
  retryButton: {
    marginTop: SPACING.sm,
    minWidth: 120,
  },
});

export default ErrorMessage;
