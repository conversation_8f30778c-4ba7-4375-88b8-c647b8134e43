import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { COLORS, FONT_SIZES, SPACING } from '../constants/AppConstants';

const LoadingSpinner = ({ 
  size = 'large', 
  color = COLORS.primary, 
  text = 'جاري التحميل...', 
  showText = true,
  style 
}) => {
  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator 
        size={size} 
        color={color} 
        style={styles.spinner}
      />
      {showText && (
        <Text style={styles.text}>{text}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  spinner: {
    marginBottom: SPACING.md,
  },
  text: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontFamily: 'System', // يمكن تغييرها لخط عربي
  },
});

export default LoadingSpinner;
