import React from 'react';
import { StyleSheet } from 'react-native';
import { Button } from 'react-native-paper';
import { COLORS, FONT_SIZES, SPACING } from '../constants/AppConstants';

const CustomButton = ({
  title,
  onPress,
  mode = 'contained',
  disabled = false,
  loading = false,
  style,
  textStyle,
  icon = null,
  buttonColor = COLORS.primary,
  textColor = COLORS.surface,
  size = 'medium', // small, medium, large
  fullWidth = false,
  ...props
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    if (size === 'small') {
      baseStyle.push(styles.smallButton);
    } else if (size === 'large') {
      baseStyle.push(styles.largeButton);
    }
    
    if (fullWidth) {
      baseStyle.push(styles.fullWidth);
    }
    
    if (style) {
      baseStyle.push(style);
    }
    
    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle = [styles.text];
    
    if (size === 'small') {
      baseStyle.push(styles.smallText);
    } else if (size === 'large') {
      baseStyle.push(styles.largeText);
    }
    
    if (textStyle) {
      baseStyle.push(textStyle);
    }
    
    return baseStyle;
  };

  return (
    <Button
      mode={mode}
      onPress={onPress}
      disabled={disabled || loading}
      loading={loading}
      style={getButtonStyle()}
      labelStyle={getTextStyle()}
      buttonColor={buttonColor}
      textColor={textColor}
      icon={icon}
      {...props}
    >
      {title}
    </Button>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    paddingVertical: SPACING.xs,
  },
  smallButton: {
    paddingVertical: SPACING.xs / 2,
  },
  largeButton: {
    paddingVertical: SPACING.sm,
  },
  fullWidth: {
    width: '100%',
  },
  text: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
    fontFamily: 'System',
  },
  smallText: {
    fontSize: FONT_SIZES.small,
  },
  largeText: {
    fontSize: FONT_SIZES.large,
  },
});

export default CustomButton;
