import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { TextInput, HelperText } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING } from '../constants/AppConstants';

const CustomTextInput = ({
  label,
  value,
  onChangeText,
  placeholder,
  error = false,
  errorText = '',
  helperText = '',
  secureTextEntry = false,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  disabled = false,
  leftIcon = null,
  rightIcon = null,
  onRightIconPress = null,
  style,
  inputStyle,
  mode = 'outlined',
  dense = false,
  maxLength = null,
  autoCapitalize = 'sentences',
  autoCorrect = true,
  ...props
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const getRightIcon = () => {
    if (secureTextEntry) {
      return (
        <TextInput.Icon
          icon={isPasswordVisible ? 'eye-off' : 'eye'}
          onPress={togglePasswordVisibility}
          iconColor={COLORS.textSecondary}
        />
      );
    }
    
    if (rightIcon) {
      return (
        <TextInput.Icon
          icon={rightIcon}
          onPress={onRightIconPress}
          iconColor={COLORS.textSecondary}
        />
      );
    }
    
    return null;
  };

  const getLeftIcon = () => {
    if (leftIcon) {
      return (
        <TextInput.Icon
          icon={leftIcon}
          iconColor={COLORS.textSecondary}
        />
      );
    }
    return null;
  };

  return (
    <View style={[styles.container, style]}>
      <TextInput
        label={label}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        error={error}
        secureTextEntry={secureTextEntry && !isPasswordVisible}
        keyboardType={keyboardType}
        multiline={multiline}
        numberOfLines={numberOfLines}
        disabled={disabled}
        mode={mode}
        dense={dense}
        maxLength={maxLength}
        autoCapitalize={autoCapitalize}
        autoCorrect={autoCorrect}
        style={[styles.input, inputStyle]}
        theme={{
          colors: {
            primary: COLORS.primary,
            error: COLORS.error,
            text: COLORS.text,
            placeholder: COLORS.textSecondary,
            background: COLORS.surface,
          },
        }}
        left={getLeftIcon()}
        right={getRightIcon()}
        {...props}
      />
      
      {(error && errorText) || helperText ? (
        <HelperText 
          type={error ? 'error' : 'info'} 
          visible={true}
          style={styles.helperText}
        >
          {error ? errorText : helperText}
        </HelperText>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.sm,
  },
  input: {
    backgroundColor: COLORS.surface,
    fontSize: FONT_SIZES.medium,
    fontFamily: 'System',
  },
  helperText: {
    fontSize: FONT_SIZES.small,
    fontFamily: 'System',
    textAlign: 'right', // للنص العربي
  },
});

export default CustomTextInput;
