import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { CHAT_STATUS, MESSAGE_TYPES } from '../constants/AppConstants';

class ChatService {
  // إنشاء محادثة جديدة
  async createChat(adId, buyerId, sellerId, adData) {
    try {
      // التحقق من وجود محادثة مسبقة
      const existingChat = await this.getChatByParticipants(adId, buyerId, sellerId);
      
      if (existingChat.success && existingChat.chat) {
        return { success: true, chat: existingChat.chat, isNew: false };
      }

      const newChat = {
        adId,
        buyerId,
        sellerId,
        participants: [buyerId, sellerId],
        status: CHAT_STATUS.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastMessage: null,
        lastMessageAt: null,
        adData: {
          title: adData.title,
          amount: adData.amount,
          currency: adData.currency,
          rate: adData.rate,
          type: adData.type
        },
        isRated: {
          [buyerId]: false,
          [sellerId]: false
        }
      };

      const docRef = await addDoc(collection(db, 'chats'), newChat);
      
      return { 
        success: true, 
        chat: { ...newChat, id: docRef.id },
        isNew: true
      };
    } catch (error) {
      console.error('خطأ في إنشاء المحادثة:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب محادثة بين مشاركين معينين
  async getChatByParticipants(adId, buyerId, sellerId) {
    try {
      const q = query(
        collection(db, 'chats'),
        where('adId', '==', adId),
        where('participants', 'array-contains', buyerId)
      );

      const querySnapshot = await getDocs(q);
      
      for (const doc of querySnapshot.docs) {
        const chatData = doc.data();
        if (chatData.participants.includes(sellerId)) {
          return { 
            success: true, 
            chat: { id: doc.id, ...chatData }
          };
        }
      }

      return { success: true, chat: null };
    } catch (error) {
      console.error('خطأ في جلب المحادثة:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب محادثات المستخدم
  async getUserChats(userId) {
    try {
      const q = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', userId),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const chats = [];
      
      querySnapshot.forEach((doc) => {
        chats.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return { success: true, chats };
    } catch (error) {
      console.error('خطأ في جلب محادثات المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // إرسال رسالة
  async sendMessage(chatId, senderId, messageData) {
    try {
      const message = {
        chatId,
        senderId,
        type: messageData.type || MESSAGE_TYPES.TEXT,
        content: messageData.content,
        imageUrl: messageData.imageUrl || null,
        createdAt: new Date(),
        isRead: false
      };

      // إضافة الرسالة إلى مجموعة الرسائل
      await addDoc(collection(db, 'messages'), message);

      // تحديث آخر رسالة في المحادثة
      await updateDoc(doc(db, 'chats', chatId), {
        lastMessage: messageData.content,
        lastMessageAt: new Date(),
        updatedAt: new Date()
      });

      return { success: true, message };
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب رسائل المحادثة
  async getChatMessages(chatId, limitCount = 50) {
    try {
      const q = query(
        collection(db, 'messages'),
        where('chatId', '==', chatId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const messages = [];
      
      querySnapshot.forEach((doc) => {
        messages.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return { success: true, messages: messages.reverse() };
    } catch (error) {
      console.error('خطأ في جلب رسائل المحادثة:', error);
      return { success: false, error: error.message };
    }
  }

  // مراقبة الرسائل الجديدة
  subscribeToMessages(chatId, callback) {
    const q = query(
      collection(db, 'messages'),
      where('chatId', '==', chatId),
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    return onSnapshot(q, (querySnapshot) => {
      const messages = [];
      querySnapshot.forEach((doc) => {
        messages.push({
          id: doc.id,
          ...doc.data()
        });
      });
      callback(messages.reverse());
    });
  }

  // تحديث حالة المحادثة
  async updateChatStatus(chatId, status) {
    try {
      await updateDoc(doc(db, 'chats', chatId), {
        status,
        updatedAt: new Date()
      });
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث حالة المحادثة:', error);
      return { success: false, error: error.message };
    }
  }

  // تحديث حالة التقييم
  async updateRatingStatus(chatId, userId, isRated) {
    try {
      await updateDoc(doc(db, 'chats', chatId), {
        [`isRated.${userId}`]: isRated,
        updatedAt: new Date()
      });
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث حالة التقييم:', error);
      return { success: false, error: error.message };
    }
  }

  // وضع علامة قراءة على الرسائل
  async markMessagesAsRead(chatId, userId) {
    try {
      const q = query(
        collection(db, 'messages'),
        where('chatId', '==', chatId),
        where('senderId', '!=', userId),
        where('isRead', '==', false)
      );

      const querySnapshot = await getDocs(q);
      
      const updatePromises = querySnapshot.docs.map(doc => 
        updateDoc(doc.ref, { isRead: true })
      );

      await Promise.all(updatePromises);
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في وضع علامة القراءة:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب عدد الرسائل غير المقروءة
  async getUnreadMessagesCount(userId) {
    try {
      const q = query(
        collection(db, 'messages'),
        where('senderId', '!=', userId),
        where('isRead', '==', false)
      );

      const querySnapshot = await getDocs(q);
      return { success: true, count: querySnapshot.size };
    } catch (error) {
      console.error('خطأ في جلب عدد الرسائل غير المقروءة:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new ChatService();
