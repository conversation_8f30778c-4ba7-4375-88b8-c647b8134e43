import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '../config/firebase';
import { FILE_LIMITS } from '../constants/AppConstants';

class StorageService {
  // رفع صورة الهوية
  async uploadIdDocument(userId, imageUri) {
    try {
      // التحقق من حجم الملف
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      if (blob.size > FILE_LIMITS.MAX_IMAGE_SIZE) {
        return { success: false, error: 'حجم الصورة كبير جداً' };
      }

      // إنشاء مرجع للملف
      const fileName = `id_documents/${userId}_${Date.now()}.jpg`;
      const storageRef = ref(storage, fileName);

      // رفع الملف
      await uploadBytes(storageRef, blob);
      
      // الحصول على رابط التحميل
      const downloadURL = await getDownloadURL(storageRef);
      
      return { success: true, url: downloadURL, path: fileName };
    } catch (error) {
      console.error('خطأ في رفع صورة الهوية:', error);
      return { success: false, error: error.message };
    }
  }

  // رفع صورة السيلفي
  async uploadSelfieDocument(userId, imageUri) {
    try {
      // التحقق من حجم الملف
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      if (blob.size > FILE_LIMITS.MAX_IMAGE_SIZE) {
        return { success: false, error: 'حجم الصورة كبير جداً' };
      }

      // إنشاء مرجع للملف
      const fileName = `selfie_documents/${userId}_${Date.now()}.jpg`;
      const storageRef = ref(storage, fileName);

      // رفع الملف
      await uploadBytes(storageRef, blob);
      
      // الحصول على رابط التحميل
      const downloadURL = await getDownloadURL(storageRef);
      
      return { success: true, url: downloadURL, path: fileName };
    } catch (error) {
      console.error('خطأ في رفع صورة السيلفي:', error);
      return { success: false, error: error.message };
    }
  }

  // رفع صورة للمحادثة
  async uploadChatImage(chatId, userId, imageUri) {
    try {
      // التحقق من حجم الملف
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      if (blob.size > FILE_LIMITS.MAX_IMAGE_SIZE) {
        return { success: false, error: 'حجم الصورة كبير جداً' };
      }

      // إنشاء مرجع للملف
      const fileName = `chat_images/${chatId}/${userId}_${Date.now()}.jpg`;
      const storageRef = ref(storage, fileName);

      // رفع الملف
      await uploadBytes(storageRef, blob);
      
      // الحصول على رابط التحميل
      const downloadURL = await getDownloadURL(storageRef);
      
      return { success: true, url: downloadURL, path: fileName };
    } catch (error) {
      console.error('خطأ في رفع صورة المحادثة:', error);
      return { success: false, error: error.message };
    }
  }

  // حذف ملف
  async deleteFile(filePath) {
    try {
      const storageRef = ref(storage, filePath);
      await deleteObject(storageRef);
      return { success: true };
    } catch (error) {
      console.error('خطأ في حذف الملف:', error);
      return { success: false, error: error.message };
    }
  }

  // التحقق من نوع الملف
  validateImageType(imageUri) {
    const extension = imageUri.split('.').pop().toLowerCase();
    const allowedExtensions = ['jpg', 'jpeg', 'png'];
    return allowedExtensions.includes(extension);
  }

  // ضغط الصورة (يمكن تطويرها لاحقاً)
  async compressImage(imageUri, quality = 0.8) {
    // يمكن استخدام مكتبة مثل expo-image-manipulator
    // للضغط وتحسين جودة الصور
    return imageUri;
  }

  // تحويل URI إلى Blob
  async uriToBlob(uri) {
    try {
      const response = await fetch(uri);
      const blob = await response.blob();
      return blob;
    } catch (error) {
      console.error('خطأ في تحويل URI إلى Blob:', error);
      throw error;
    }
  }

  // الحصول على معلومات الملف
  async getFileInfo(uri) {
    try {
      const response = await fetch(uri);
      const blob = await response.blob();
      
      return {
        size: blob.size,
        type: blob.type,
        sizeInMB: (blob.size / (1024 * 1024)).toFixed(2)
      };
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الملف:', error);
      return null;
    }
  }
}

export default new StorageService();
