import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy,
  runTransaction 
} from 'firebase/firestore';
import { db } from '../config/firebase';

class RatingService {
  // إضافة تقييم جديد
  async addRating(ratingData) {
    try {
      // التحقق من عدم وجود تقييم مسبق
      const existingRating = await this.getRatingByTransaction(
        ratingData.raterId, 
        ratingData.ratedUserId, 
        ratingData.chatId
      );

      if (existingRating.success && existingRating.rating) {
        return { success: false, error: 'تم إرسال التقييم مسبقاً' };
      }

      const newRating = {
        ...ratingData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // إضافة التقييم
      const docRef = await addDoc(collection(db, 'ratings'), newRating);

      // تحديث متوسط تقييم المستخدم
      await this.updateUserRating(ratingData.ratedUserId);

      return { 
        success: true, 
        ratingId: docRef.id,
        rating: { ...newRating, id: docRef.id }
      };
    } catch (error) {
      console.error('خطأ في إضافة التقييم:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب تقييم بناءً على المعاملة
  async getRatingByTransaction(raterId, ratedUserId, chatId) {
    try {
      const q = query(
        collection(db, 'ratings'),
        where('raterId', '==', raterId),
        where('ratedUserId', '==', ratedUserId),
        where('chatId', '==', chatId)
      );

      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        return { 
          success: true, 
          rating: { id: doc.id, ...doc.data() }
        };
      }

      return { success: true, rating: null };
    } catch (error) {
      console.error('خطأ في جلب التقييم:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب تقييمات مستخدم معين
  async getUserRatings(userId, limitCount = 20) {
    try {
      const q = query(
        collection(db, 'ratings'),
        where('ratedUserId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const ratings = [];
      
      querySnapshot.forEach((doc) => {
        ratings.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return { success: true, ratings };
    } catch (error) {
      console.error('خطأ في جلب تقييمات المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // حساب متوسط تقييم المستخدم
  async calculateUserRating(userId) {
    try {
      const q = query(
        collection(db, 'ratings'),
        where('ratedUserId', '==', userId)
      );

      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return { success: true, averageRating: 0, totalRatings: 0 };
      }

      let totalRating = 0;
      let totalCount = 0;

      querySnapshot.forEach((doc) => {
        const rating = doc.data();
        totalRating += rating.rating;
        totalCount++;
      });

      const averageRating = totalRating / totalCount;

      return { 
        success: true, 
        averageRating: Math.round(averageRating * 10) / 10, // تقريب لرقم عشري واحد
        totalRatings: totalCount 
      };
    } catch (error) {
      console.error('خطأ في حساب متوسط التقييم:', error);
      return { success: false, error: error.message };
    }
  }

  // تحديث متوسط تقييم المستخدم في مستند المستخدم
  async updateUserRating(userId) {
    try {
      const ratingResult = await this.calculateUserRating(userId);
      
      if (ratingResult.success) {
        await updateDoc(doc(db, 'users', userId), {
          rating: ratingResult.averageRating,
          totalRatings: ratingResult.totalRatings,
          updatedAt: new Date()
        });
      }

      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث تقييم المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // تحديث تقييم موجود
  async updateRating(ratingId, updateData) {
    try {
      await updateDoc(doc(db, 'ratings', ratingId), {
        ...updateData,
        updatedAt: new Date()
      });

      // إعادة حساب متوسط التقييم
      const ratingDoc = await getDoc(doc(db, 'ratings', ratingId));
      if (ratingDoc.exists()) {
        const ratingData = ratingDoc.data();
        await this.updateUserRating(ratingData.ratedUserId);
      }

      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث التقييم:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب إحصائيات التقييمات
  async getRatingStats(userId) {
    try {
      const q = query(
        collection(db, 'ratings'),
        where('ratedUserId', '==', userId)
      );

      const querySnapshot = await getDocs(q);
      
      const stats = {
        1: 0, 2: 0, 3: 0, 4: 0, 5: 0
      };

      let totalRating = 0;
      let totalCount = 0;

      querySnapshot.forEach((doc) => {
        const rating = doc.data();
        stats[rating.rating]++;
        totalRating += rating.rating;
        totalCount++;
      });

      const averageRating = totalCount > 0 ? totalRating / totalCount : 0;

      return { 
        success: true, 
        stats,
        averageRating: Math.round(averageRating * 10) / 10,
        totalRatings: totalCount 
      };
    } catch (error) {
      console.error('خطأ في جلب إحصائيات التقييمات:', error);
      return { success: false, error: error.message };
    }
  }

  // التحقق من إمكانية التقييم
  async canRate(raterId, ratedUserId, chatId) {
    try {
      // التحقق من عدم تقييم النفس
      if (raterId === ratedUserId) {
        return { success: false, error: 'لا يمكن تقييم نفسك' };
      }

      // التحقق من عدم وجود تقييم مسبق
      const existingRating = await this.getRatingByTransaction(raterId, ratedUserId, chatId);
      
      if (existingRating.success && existingRating.rating) {
        return { success: false, error: 'تم إرسال التقييم مسبقاً' };
      }

      return { success: true };
    } catch (error) {
      console.error('خطأ في التحقق من إمكانية التقييم:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب أحدث التقييمات
  async getRecentRatings(userId, limitCount = 5) {
    try {
      const q = query(
        collection(db, 'ratings'),
        where('ratedUserId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const ratings = [];
      
      querySnapshot.forEach((doc) => {
        ratings.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return { success: true, ratings };
    } catch (error) {
      console.error('خطأ في جلب أحدث التقييمات:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new RatingService();
