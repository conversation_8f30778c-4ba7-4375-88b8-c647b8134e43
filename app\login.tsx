import React, { useState } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView, 
  KeyboardAvoidingView, 
  Platform,
  Alert 
} from 'react-native';
import { Text, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import CustomTextInput from '../components/CustomTextInput';
import CustomButton from '../components/CustomButton';
import LoadingSpinner from '../components/LoadingSpinner';
import AuthService from '../services/AuthService';
import { COLORS, FONT_SIZES, SPACING } from '../constants/AppConstants';

export default function LoginScreen() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const newErrors = {};

    // التحقق من البريد الإلكتروني
    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من كلمة المرور
    if (!formData.password.trim()) {
      newErrors.password = 'كلمة المرور مطلوبة';
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const result = await AuthService.loginUser(formData.email, formData.password);
      
      if (result.success) {
        // التحقق من حالة المستخدم
        const user = result.user;
        
        if (user.status === 'pending_verification') {
          Alert.alert(
            'حساب قيد المراجعة',
            'حسابك قيد المراجعة من قبل الإدارة. سيتم إشعارك عند الموافقة على حسابك.',
            [{ text: 'موافق' }]
          );
        } else if (user.status === 'rejected') {
          Alert.alert(
            'تم رفض الحساب',
            'تم رفض حسابك من قبل الإدارة. يرجى التواصل مع الدعم الفني.',
            [{ text: 'موافق' }]
          );
        } else if (user.status === 'verified') {
          // الانتقال إلى الشاشة الرئيسية
          router.replace('/(tabs)');
        }
      } else {
        Alert.alert('خطأ في تسجيل الدخول', result.error);
      }
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ غير متوقع');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // إزالة رسالة الخطأ عند بدء الكتابة
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (loading) {
    return <LoadingSpinner text="جاري تسجيل الدخول..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.title}>صرفي DZ</Text>
            <Text style={styles.subtitle}>سوق صرف العملات الجزائري</Text>
          </View>

          <Card style={styles.card}>
            <Card.Content style={styles.cardContent}>
              <Text style={styles.formTitle}>تسجيل الدخول</Text>

              <CustomTextInput
                label="البريد الإلكتروني"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                placeholder="أدخل بريدك الإلكتروني"
                keyboardType="email-address"
                autoCapitalize="none"
                leftIcon="email"
                error={!!errors.email}
                errorText={errors.email}
              />

              <CustomTextInput
                label="كلمة المرور"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                placeholder="أدخل كلمة المرور"
                secureTextEntry={true}
                leftIcon="lock"
                error={!!errors.password}
                errorText={errors.password}
              />

              <CustomButton
                title="تسجيل الدخول"
                onPress={handleLogin}
                fullWidth
                size="large"
                style={styles.loginButton}
              />

              <CustomButton
                title="نسيت كلمة المرور؟"
                mode="text"
                onPress={() => router.push('/forgot-password')}
                textColor={COLORS.primary}
                style={styles.forgotButton}
              />
            </Card.Content>
          </Card>

          <View style={styles.footer}>
            <Text style={styles.footerText}>ليس لديك حساب؟</Text>
            <CustomButton
              title="إنشاء حساب جديد"
              mode="outlined"
              onPress={() => router.push('/register')}
              style={styles.registerButton}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SPACING.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.header,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
    fontFamily: 'System',
  },
  subtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontFamily: 'System',
  },
  card: {
    backgroundColor: COLORS.surface,
    elevation: 4,
    borderRadius: 12,
  },
  cardContent: {
    padding: SPACING.lg,
  },
  formTitle: {
    fontSize: FONT_SIZES.title,
    fontWeight: 'bold',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    fontFamily: 'System',
  },
  loginButton: {
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  forgotButton: {
    alignSelf: 'center',
  },
  footer: {
    alignItems: 'center',
    marginTop: SPACING.xl,
  },
  footerText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    fontFamily: 'System',
  },
  registerButton: {
    minWidth: 200,
  },
});
