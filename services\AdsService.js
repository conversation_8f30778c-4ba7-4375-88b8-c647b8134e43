import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  startAfter 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { AD_STATUS, AD_TYPES } from '../constants/AppConstants';

class AdsService {
  // إنشاء إعلان جديد
  async createAd(adData) {
    try {
      const newAd = {
        ...adData,
        status: AD_STATUS.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
        views: 0,
        isActive: true
      };

      const docRef = await addDoc(collection(db, 'ads'), newAd);
      
      return { 
        success: true, 
        adId: docRef.id,
        ad: { ...newAd, id: docRef.id }
      };
    } catch (error) {
      console.error('خطأ في إنشاء الإعلان:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب جميع الإعلانات النشطة
  async getActiveAds(lastDoc = null, limitCount = 20) {
    try {
      let q = query(
        collection(db, 'ads'),
        where('status', '==', AD_STATUS.ACTIVE),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const querySnapshot = await getDocs(q);
      const ads = [];
      
      querySnapshot.forEach((doc) => {
        ads.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return { 
        success: true, 
        ads,
        lastDoc: querySnapshot.docs[querySnapshot.docs.length - 1]
      };
    } catch (error) {
      console.error('خطأ في جلب الإعلانات:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب إعلانات مستخدم معين
  async getUserAds(userId) {
    try {
      const q = query(
        collection(db, 'ads'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const ads = [];
      
      querySnapshot.forEach((doc) => {
        ads.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return { success: true, ads };
    } catch (error) {
      console.error('خطأ في جلب إعلانات المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // البحث في الإعلانات
  async searchAds(filters) {
    try {
      let q = query(
        collection(db, 'ads'),
        where('status', '==', AD_STATUS.ACTIVE),
        where('isActive', '==', true)
      );

      // تطبيق الفلاتر
      if (filters.currency) {
        q = query(q, where('currency', '==', filters.currency));
      }

      if (filters.type) {
        q = query(q, where('type', '==', filters.type));
      }

      if (filters.paymentMethod) {
        q = query(q, where('paymentMethod', '==', filters.paymentMethod));
      }

      q = query(q, orderBy('createdAt', 'desc'), limit(50));

      const querySnapshot = await getDocs(q);
      const ads = [];
      
      querySnapshot.forEach((doc) => {
        const adData = doc.data();
        
        // تطبيق فلاتر إضافية
        let includeAd = true;
        
        if (filters.minAmount && adData.amount < filters.minAmount) {
          includeAd = false;
        }
        
        if (filters.maxAmount && adData.amount > filters.maxAmount) {
          includeAd = false;
        }
        
        if (includeAd) {
          ads.push({
            id: doc.id,
            ...adData
          });
        }
      });

      return { success: true, ads };
    } catch (error) {
      console.error('خطأ في البحث في الإعلانات:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب إعلان واحد
  async getAdById(adId) {
    try {
      const docRef = doc(db, 'ads', adId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        // زيادة عدد المشاهدات
        await this.incrementViews(adId);
        
        return { 
          success: true, 
          ad: { id: docSnap.id, ...docSnap.data() }
        };
      } else {
        return { success: false, error: 'الإعلان غير موجود' };
      }
    } catch (error) {
      console.error('خطأ في جلب الإعلان:', error);
      return { success: false, error: error.message };
    }
  }

  // تحديث إعلان
  async updateAd(adId, updateData) {
    try {
      await updateDoc(doc(db, 'ads', adId), {
        ...updateData,
        updatedAt: new Date()
      });
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث الإعلان:', error);
      return { success: false, error: error.message };
    }
  }

  // حذف إعلان
  async deleteAd(adId) {
    try {
      await deleteDoc(doc(db, 'ads', adId));
      return { success: true };
    } catch (error) {
      console.error('خطأ في حذف الإعلان:', error);
      return { success: false, error: error.message };
    }
  }

  // إلغاء تفعيل إعلان
  async deactivateAd(adId) {
    try {
      await updateDoc(doc(db, 'ads', adId), {
        isActive: false,
        status: AD_STATUS.INACTIVE,
        updatedAt: new Date()
      });
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في إلغاء تفعيل الإعلان:', error);
      return { success: false, error: error.message };
    }
  }

  // تفعيل إعلان
  async activateAd(adId) {
    try {
      await updateDoc(doc(db, 'ads', adId), {
        isActive: true,
        status: AD_STATUS.ACTIVE,
        updatedAt: new Date()
      });
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في تفعيل الإعلان:', error);
      return { success: false, error: error.message };
    }
  }

  // زيادة عدد المشاهدات
  async incrementViews(adId) {
    try {
      const docRef = doc(db, 'ads', adId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const currentViews = docSnap.data().views || 0;
        await updateDoc(docRef, {
          views: currentViews + 1
        });
      }
    } catch (error) {
      console.error('خطأ في زيادة المشاهدات:', error);
    }
  }

  // تحديث حالة الإعلان إلى مكتمل
  async markAdAsCompleted(adId) {
    try {
      await updateDoc(doc(db, 'ads', adId), {
        status: AD_STATUS.COMPLETED,
        completedAt: new Date(),
        updatedAt: new Date()
      });
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث حالة الإعلان:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new AdsService();
