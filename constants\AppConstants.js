// ثوابت التطبيق
export const APP_NAME = 'صرفي DZ';

// حالات المستخدم
export const USER_STATUS = {
  PENDING_VERIFICATION: 'pending_verification',
  VERIFIED: 'verified',
  REJECTED: 'rejected',
  SUSPENDED: 'suspended'
};

// أنواع العملات
export const CURRENCIES = {
  DZD: { code: 'DZD', name: 'دينار جزائري', symbol: 'د.ج' },
  EUR: { code: 'EUR', name: 'يورو', symbol: '€' },
  USD: { code: 'USD', name: 'دولار أمريكي', symbol: '$' }
};

// طرق الدفع
export const PAYMENT_METHODS = [
  'CCP',
  'تحويل بنكي',
  'يداً بيد',
  'BaridiMob',
  'Western Union',
  'MoneyGram',
  'أخرى'
];

// أنواع الإعلانات
export const AD_TYPES = {
  BUY: 'buy',   // شراء
  SELL: 'sell'  // بيع
};

// حالات الإعلانات
export const AD_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// حالات المحادثات
export const CHAT_STATUS = {
  ACTIVE: 'active',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// أنواع الرسائل
export const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  SYSTEM: 'system'
};

// ألوان التطبيق
export const COLORS = {
  primary: '#2E7D32',      // أخضر داكن
  primaryLight: '#4CAF50', // أخضر فاتح
  secondary: '#FF9800',    // برتقالي
  background: '#F5F5F5',   // رمادي فاتح
  surface: '#FFFFFF',      // أبيض
  error: '#F44336',        // أحمر
  warning: '#FF9800',      // برتقالي
  success: '#4CAF50',      // أخضر
  text: '#212121',         // أسود
  textSecondary: '#757575', // رمادي
  border: '#E0E0E0',       // رمادي فاتح
  disabled: '#BDBDBD'      // رمادي متوسط
};

// أحجام الخط
export const FONT_SIZES = {
  small: 12,
  medium: 14,
  large: 16,
  xlarge: 18,
  xxlarge: 20,
  title: 24,
  header: 28
};

// المسافات
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 40
};

// أبعاد الشاشة
export const SCREEN_DIMENSIONS = {
  fullHeight: '100%',
  fullWidth: '100%'
};

// رسائل الخطأ
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'خطأ في الاتصال بالإنترنت',
  INVALID_EMAIL: 'البريد الإلكتروني غير صحيح',
  WEAK_PASSWORD: 'كلمة المرور ضعيفة جداً',
  EMAIL_ALREADY_EXISTS: 'البريد الإلكتروني مستخدم مسبقاً',
  USER_NOT_FOUND: 'المستخدم غير موجود',
  WRONG_PASSWORD: 'كلمة المرور خاطئة',
  VERIFICATION_PENDING: 'حسابك قيد المراجعة',
  ACCOUNT_REJECTED: 'تم رفض حسابك',
  ACCOUNT_SUSPENDED: 'تم تعليق حسابك'
};

// رسائل النجاح
export const SUCCESS_MESSAGES = {
  ACCOUNT_CREATED: 'تم إنشاء الحساب بنجاح',
  LOGIN_SUCCESS: 'تم تسجيل الدخول بنجاح',
  DOCUMENTS_UPLOADED: 'تم رفع الوثائق بنجاح',
  AD_CREATED: 'تم إنشاء الإعلان بنجاح',
  MESSAGE_SENT: 'تم إرسال الرسالة',
  RATING_SUBMITTED: 'تم إرسال التقييم'
};

// إعدادات الكاميرا
export const CAMERA_OPTIONS = {
  mediaTypes: 'Images',
  allowsEditing: true,
  aspect: [4, 3],
  quality: 0.8
};

// حدود الملفات
export const FILE_LIMITS = {
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/jpg']
};

// إعدادات التقييم
export const RATING_CONFIG = {
  MIN_RATING: 1,
  MAX_RATING: 5,
  DEFAULT_RATING: 5
};

// إعدادات الصفحات
export const PAGINATION = {
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 50
};
