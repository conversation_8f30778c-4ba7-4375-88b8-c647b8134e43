import React, { useState, useEffect } from 'react';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Provider as PaperProvider } from 'react-native-paper';
import { I18nManager } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import AuthService from '../services/AuthService';
import LoadingSpinner from '../components/LoadingSpinner';

// إعداد RTL للنص العربي
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

// موضوع التطبيق
const paperTheme = {
  colors: {
    primary: '#2E7D32',
    accent: '#FF9800',
    background: '#F5F5F5',
    surface: '#FFFFFF',
    text: '#212121',
    disabled: '#BDBDBD',
    placeholder: '#757575',
    backdrop: 'rgba(0, 0, 0, 0.5)',
  },
};

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  useEffect(() => {
    // مراقبة حالة المصادقة
    const unsubscribe = AuthService.onAuthStateChanged(async (authUser) => {
      if (authUser) {
        // جلب بيانات المستخدم من Firestore
        const userResult = await AuthService.getUserData(authUser.uid);
        if (userResult.success) {
          setUser(userResult.user);

          // توجيه المستخدم حسب حالته
          if (userResult.user.status === 'verified') {
            router.replace('/(tabs)');
          } else {
            // المستخدم غير محقق - البقاء في الشاشة الحالية
          }
        } else {
          setUser(null);
          router.replace('/login');
        }
      } else {
        setUser(null);
        router.replace('/login');
      }
      setIsLoading(false);
    });

    return unsubscribe;
  }, []);

  if (!loaded || isLoading) {
    return <LoadingSpinner text="جاري تحميل التطبيق..." />;
  }

  return (
    <PaperProvider theme={paperTheme}>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="login" />
          <Stack.Screen name="register" />
          <Stack.Screen name="document-upload" />
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="dark" backgroundColor="#2E7D32" />
      </ThemeProvider>
    </PaperProvider>
  );
}
