import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING } from '../../constants/AppConstants';

export default function HomeScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <MaterialIcons name="currency-exchange" size={64} color={COLORS.primary} />
          <Text style={styles.title}>صرفي DZ</Text>
          <Text style={styles.subtitle}>سوق صرف العملات الجزائري</Text>
        </View>

        <Card style={styles.card}>
          <Card.Content style={styles.cardContent}>
            <Text style={styles.welcomeText}>مرحباً بك في صرفي DZ</Text>
            <Text style={styles.descriptionText}>
              منصة آمنة وموثوقة لتبادل العملات في الجزائر
            </Text>

            <View style={styles.featuresContainer}>
              <View style={styles.feature}>
                <MaterialIcons name="security" size={24} color={COLORS.primary} />
                <Text style={styles.featureText}>آمن وموثوق</Text>
              </View>

              <View style={styles.feature}>
                <MaterialIcons name="verified-user" size={24} color={COLORS.primary} />
                <Text style={styles.featureText}>مستخدمون محققون</Text>
              </View>

              <View style={styles.feature}>
                <MaterialIcons name="chat" size={24} color={COLORS.primary} />
                <Text style={styles.featureText}>دردشة مباشرة</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        <Card style={styles.statusCard}>
          <Card.Content>
            <Text style={styles.statusTitle}>حالة التطوير</Text>
            <Text style={styles.statusText}>
              التطبيق قيد التطوير. الميزات الأساسية جاهزة:
            </Text>
            <Text style={styles.statusList}>
              ✅ نظام التسجيل والدخول{'\n'}
              ✅ رفع وثائق الهوية{'\n'}
              ✅ خدمات Firebase{'\n'}
              🔄 السوق والإعلانات (قيد التطوير){'\n'}
              🔄 نظام الدردشة (قيد التطوير){'\n'}
              🔄 نظام التقييم (قيد التطوير)
            </Text>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
    marginTop: SPACING.lg,
  },
  title: {
    fontSize: FONT_SIZES.header,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginTop: SPACING.md,
    fontFamily: 'System',
  },
  subtitle: {
    fontSize: FONT_SIZES.large,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
    fontFamily: 'System',
  },
  card: {
    backgroundColor: COLORS.surface,
    elevation: 4,
    borderRadius: 12,
    marginBottom: SPACING.lg,
  },
  cardContent: {
    padding: SPACING.lg,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: FONT_SIZES.title,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.sm,
    fontFamily: 'System',
  },
  descriptionText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    lineHeight: 22,
    fontFamily: 'System',
  },
  featuresContainer: {
    width: '100%',
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  featureText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.text,
    marginLeft: SPACING.sm,
    fontFamily: 'System',
  },
  statusCard: {
    backgroundColor: COLORS.surface,
    elevation: 2,
    borderRadius: 12,
  },
  statusTitle: {
    fontSize: FONT_SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
    fontFamily: 'System',
  },
  statusText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    fontFamily: 'System',
  },
  statusList: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.text,
    lineHeight: 24,
    fontFamily: 'System',
  },
});
