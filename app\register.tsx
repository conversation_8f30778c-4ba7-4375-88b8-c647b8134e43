import React, { useState } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView, 
  KeyboardAvoidingView, 
  Platform,
  Alert 
} from 'react-native';
import { Text, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import CustomTextInput from '../components/CustomTextInput';
import CustomButton from '../components/CustomButton';
import LoadingSpinner from '../components/LoadingSpinner';
import AuthService from '../services/AuthService';
import { COLORS, FONT_SIZES, SPACING } from '../constants/AppConstants';

export default function RegisterScreen() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const newErrors = {};

    // التحقق من الاسم الكامل
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'الاسم الكامل مطلوب';
    } else if (formData.fullName.trim().length < 3) {
      newErrors.fullName = 'الاسم يجب أن يكون 3 أحرف على الأقل';
    }

    // التحقق من البريد الإلكتروني
    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من رقم الهاتف
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'رقم الهاتف مطلوب';
    } else if (!/^(05|06|07)\d{8}$/.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'رقم الهاتف غير صحيح (مثال: **********)';
    }

    // التحقق من كلمة المرور
    if (!formData.password.trim()) {
      newErrors.password = 'كلمة المرور مطلوبة';
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    // التحقق من تأكيد كلمة المرور
    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const userData = {
        fullName: formData.fullName.trim(),
        phoneNumber: formData.phoneNumber.trim()
      };

      const result = await AuthService.registerUser(
        formData.email.trim(), 
        formData.password, 
        userData
      );
      
      if (result.success) {
        Alert.alert(
          'تم إنشاء الحساب بنجاح',
          'تم إنشاء حسابك بنجاح. يرجى رفع وثائق الهوية للتحقق من حسابك.',
          [
            {
              text: 'موافق',
              onPress: () => router.push('/document-upload')
            }
          ]
        );
      } else {
        Alert.alert('خطأ في التسجيل', result.error);
      }
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ غير متوقع');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // إزالة رسالة الخطأ عند بدء الكتابة
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (loading) {
    return <LoadingSpinner text="جاري إنشاء الحساب..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.title}>إنشاء حساب جديد</Text>
            <Text style={styles.subtitle}>انضم إلى سوق صرف العملات</Text>
          </View>

          <Card style={styles.card}>
            <Card.Content style={styles.cardContent}>
              <CustomTextInput
                label="الاسم الكامل"
                value={formData.fullName}
                onChangeText={(value) => handleInputChange('fullName', value)}
                placeholder="أدخل اسمك الكامل"
                leftIcon="account"
                error={!!errors.fullName}
                errorText={errors.fullName}
              />

              <CustomTextInput
                label="البريد الإلكتروني"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                placeholder="أدخل بريدك الإلكتروني"
                keyboardType="email-address"
                autoCapitalize="none"
                leftIcon="email"
                error={!!errors.email}
                errorText={errors.email}
              />

              <CustomTextInput
                label="رقم الهاتف"
                value={formData.phoneNumber}
                onChangeText={(value) => handleInputChange('phoneNumber', value)}
                placeholder="**********"
                keyboardType="phone-pad"
                leftIcon="phone"
                error={!!errors.phoneNumber}
                errorText={errors.phoneNumber}
              />

              <CustomTextInput
                label="كلمة المرور"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                placeholder="أدخل كلمة المرور"
                secureTextEntry={true}
                leftIcon="lock"
                error={!!errors.password}
                errorText={errors.password}
              />

              <CustomTextInput
                label="تأكيد كلمة المرور"
                value={formData.confirmPassword}
                onChangeText={(value) => handleInputChange('confirmPassword', value)}
                placeholder="أعد إدخال كلمة المرور"
                secureTextEntry={true}
                leftIcon="lock-check"
                error={!!errors.confirmPassword}
                errorText={errors.confirmPassword}
              />

              <CustomButton
                title="إنشاء الحساب"
                onPress={handleRegister}
                fullWidth
                size="large"
                style={styles.registerButton}
              />
            </Card.Content>
          </Card>

          <View style={styles.footer}>
            <Text style={styles.footerText}>لديك حساب بالفعل؟</Text>
            <CustomButton
              title="تسجيل الدخول"
              mode="text"
              onPress={() => router.push('/login')}
              textColor={COLORS.primary}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
    marginTop: SPACING.lg,
  },
  title: {
    fontSize: FONT_SIZES.title,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
    fontFamily: 'System',
  },
  subtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontFamily: 'System',
  },
  card: {
    backgroundColor: COLORS.surface,
    elevation: 4,
    borderRadius: 12,
  },
  cardContent: {
    padding: SPACING.lg,
  },
  registerButton: {
    marginTop: SPACING.lg,
  },
  footer: {
    alignItems: 'center',
    marginTop: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  footerText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
    fontFamily: 'System',
  },
});
