import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  updateProfile,
  sendPasswordResetEmail 
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { USER_STATUS } from '../constants/AppConstants';

class AuthService {
  // تسجيل مستخدم جديد
  async registerUser(email, password, userData) {
    try {
      // إنشاء حساب في Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // تحديث الملف الشخصي
      await updateProfile(user, {
        displayName: userData.fullName
      });

      // إنشاء مستند المستخدم في Firestore
      const userDoc = {
        uid: user.uid,
        email: user.email,
        fullName: userData.fullName,
        phoneNumber: userData.phoneNumber,
        status: USER_STATUS.PENDING_VERIFICATION,
        createdAt: new Date(),
        updatedAt: new Date(),
        rating: 0,
        totalDeals: 0,
        completedDeals: 0,
        isVerified: false,
        documents: {
          idDocument: null,
          selfieDocument: null,
          uploadedAt: null
        }
      };

      await setDoc(doc(db, 'users', user.uid), userDoc);

      return { success: true, user: userDoc };
    } catch (error) {
      console.error('خطأ في التسجيل:', error);
      return { success: false, error: error.message };
    }
  }

  // تسجيل الدخول
  async loginUser(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // جلب بيانات المستخدم من Firestore
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        return { success: true, user: userData };
      } else {
        return { success: false, error: 'بيانات المستخدم غير موجودة' };
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      return { success: false, error: error.message };
    }
  }

  // تسجيل الخروج
  async logoutUser() {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      return { success: false, error: error.message };
    }
  }

  // إعادة تعيين كلمة المرور
  async resetPassword(email) {
    try {
      await sendPasswordResetEmail(auth, email);
      return { success: true };
    } catch (error) {
      console.error('خطأ في إعادة تعيين كلمة المرور:', error);
      return { success: false, error: error.message };
    }
  }

  // جلب بيانات المستخدم الحالي
  async getCurrentUser() {
    try {
      const user = auth.currentUser;
      if (user) {
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (userDoc.exists()) {
          return { success: true, user: userDoc.data() };
        }
      }
      return { success: false, error: 'المستخدم غير مسجل الدخول' };
    } catch (error) {
      console.error('خطأ في جلب بيانات المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // تحديث بيانات المستخدم
  async updateUserData(uid, updateData) {
    try {
      await updateDoc(doc(db, 'users', uid), {
        ...updateData,
        updatedAt: new Date()
      });
      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث بيانات المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // تحديث حالة المستخدم
  async updateUserStatus(uid, status) {
    try {
      await updateDoc(doc(db, 'users', uid), {
        status: status,
        updatedAt: new Date()
      });
      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث حالة المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // التحقق من حالة المستخدم
  async checkUserStatus(uid) {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        const userData = userDoc.data();
        return { success: true, status: userData.status };
      }
      return { success: false, error: 'المستخدم غير موجود' };
    } catch (error) {
      console.error('خطأ في التحقق من حالة المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // تحديث وثائق المستخدم
  async updateUserDocuments(uid, documentsData) {
    try {
      await updateDoc(doc(db, 'users', uid), {
        ...documentsData,
        updatedAt: new Date()
      });

      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث وثائق المستخدم:', error);
      return { success: false, error: error.message };
    }
  }

  // مراقبة حالة المصادقة
  onAuthStateChanged(callback) {
    return auth.onAuthStateChanged(callback);
  }
}

export default new AuthService();
