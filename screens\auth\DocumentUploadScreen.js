import React, { useState } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView, 
  Alert,
  Image,
  TouchableOpacity 
} from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import CustomButton from '../../components/CustomButton';
import LoadingSpinner from '../../components/LoadingSpinner';
import StorageService from '../../services/StorageService';
import AuthService from '../../services/AuthService';
import { COLORS, FONT_SIZES, SPACING, CAMERA_OPTIONS } from '../../constants/AppConstants';

const DocumentUploadScreen = ({ navigation }) => {
  const [documents, setDocuments] = useState({
    idFront: null,
    idBack: null,
    selfie: null
  });
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('خطأ', 'نحتاج إلى إذن الوصول للصور لرفع الوثائق');
      return false;
    }
    return true;
  };

  const pickImage = async (documentType) => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setDocuments(prev => ({
          ...prev,
          [documentType]: result.assets[0]
        }));
      }
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في اختيار الصورة');
    }
  };

  const takePhoto = async (documentType) => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('خطأ', 'نحتاج إلى إذن الوصول للكاميرا');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setDocuments(prev => ({
          ...prev,
          [documentType]: result.assets[0]
        }));
      }
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في التقاط الصورة');
    }
  };

  const showImagePicker = (documentType) => {
    Alert.alert(
      'اختر طريقة الرفع',
      'كيف تريد رفع الوثيقة؟',
      [
        { text: 'الكاميرا', onPress: () => takePhoto(documentType) },
        { text: 'المعرض', onPress: () => pickImage(documentType) },
        { text: 'إلغاء', style: 'cancel' }
      ]
    );
  };

  const uploadDocuments = async () => {
    if (!documents.idFront || !documents.idBack || !documents.selfie) {
      Alert.alert('خطأ', 'يرجى رفع جميع الوثائق المطلوبة');
      return;
    }

    setUploading(true);
    try {
      const currentUser = await AuthService.getCurrentUser();
      if (!currentUser) {
        Alert.alert('خطأ', 'يرجى تسجيل الدخول أولاً');
        return;
      }

      // رفع الوثائق
      const uploadPromises = [
        StorageService.uploadIdDocument(documents.idFront, currentUser.uid, 'front'),
        StorageService.uploadIdDocument(documents.idBack, currentUser.uid, 'back'),
        StorageService.uploadSelfie(documents.selfie, currentUser.uid)
      ];

      const results = await Promise.all(uploadPromises);
      
      // التحقق من نجاح جميع الرفعات
      const allSuccessful = results.every(result => result.success);
      
      if (allSuccessful) {
        // تحديث حالة المستخدم
        const updateResult = await AuthService.updateUserDocuments(currentUser.uid, {
          idFrontUrl: results[0].downloadURL,
          idBackUrl: results[1].downloadURL,
          selfieUrl: results[2].downloadURL,
          documentsUploaded: true,
          documentsUploadedAt: new Date()
        });

        if (updateResult.success) {
          Alert.alert(
            'تم رفع الوثائق بنجاح',
            'تم رفع وثائقك بنجاح. سيتم مراجعتها من قبل الإدارة وإشعارك بالنتيجة.',
            [
              {
                text: 'موافق',
                onPress: () => navigation.replace('Login')
              }
            ]
          );
        } else {
          Alert.alert('خطأ', 'حدث خطأ في حفظ معلومات الوثائق');
        }
      } else {
        Alert.alert('خطأ', 'حدث خطأ في رفع بعض الوثائق');
      }
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ غير متوقع');
    } finally {
      setUploading(false);
    }
  };

  const DocumentCard = ({ title, description, documentType, icon }) => (
    <Card style={styles.documentCard}>
      <Card.Content style={styles.cardContent}>
        <View style={styles.cardHeader}>
          <MaterialIcons name={icon} size={24} color={COLORS.primary} />
          <Text style={styles.cardTitle}>{title}</Text>
        </View>
        
        <Text style={styles.cardDescription}>{description}</Text>
        
        {documents[documentType] ? (
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: documents[documentType].uri }} 
              style={styles.previewImage}
            />
            <TouchableOpacity 
              style={styles.changeButton}
              onPress={() => showImagePicker(documentType)}
            >
              <Text style={styles.changeButtonText}>تغيير</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <CustomButton
            title="رفع الوثيقة"
            mode="outlined"
            onPress={() => showImagePicker(documentType)}
            icon="camera"
            style={styles.uploadButton}
          />
        )}
      </Card.Content>
    </Card>
  );

  if (uploading) {
    return <LoadingSpinner text="جاري رفع الوثائق..." />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.title}>رفع وثائق الهوية</Text>
          <Text style={styles.subtitle}>
            يرجى رفع الوثائق التالية للتحقق من هويتك
          </Text>
        </View>

        <DocumentCard
          title="الوجه الأمامي لبطاقة الهوية"
          description="صورة واضحة للوجه الأمامي لبطاقة الهوية الوطنية"
          documentType="idFront"
          icon="credit-card"
        />

        <DocumentCard
          title="الوجه الخلفي لبطاقة الهوية"
          description="صورة واضحة للوجه الخلفي لبطاقة الهوية الوطنية"
          documentType="idBack"
          icon="credit-card"
        />

        <DocumentCard
          title="صورة شخصية (سيلفي)"
          description="صورة شخصية واضحة تظهر وجهك بوضوح"
          documentType="selfie"
          icon="face"
        />

        <View style={styles.footer}>
          <Text style={styles.noteText}>
            ملاحظة: تأكد من وضوح جميع الوثائق وأن المعلومات مقروءة بوضوح
          </Text>
          
          <CustomButton
            title="رفع الوثائق"
            onPress={uploadDocuments}
            fullWidth
            size="large"
            disabled={!documents.idFront || !documents.idBack || !documents.selfie}
            style={styles.submitButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    padding: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.title,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
    fontFamily: 'System',
  },
  subtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    fontFamily: 'System',
  },
  documentCard: {
    backgroundColor: COLORS.surface,
    elevation: 2,
    borderRadius: 12,
    marginBottom: SPACING.lg,
  },
  cardContent: {
    padding: SPACING.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  cardTitle: {
    fontSize: FONT_SIZES.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginLeft: SPACING.sm,
    fontFamily: 'System',
  },
  cardDescription: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.textSecondary,
    marginBottom: SPACING.lg,
    lineHeight: 20,
    fontFamily: 'System',
  },
  imageContainer: {
    alignItems: 'center',
  },
  previewImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
    marginBottom: SPACING.sm,
  },
  changeButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderRadius: 6,
  },
  changeButtonText: {
    color: COLORS.surface,
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
    fontFamily: 'System',
  },
  uploadButton: {
    alignSelf: 'center',
    minWidth: 150,
  },
  footer: {
    marginTop: SPACING.lg,
  },
  noteText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    lineHeight: 18,
    fontFamily: 'System',
  },
  submitButton: {
    marginTop: SPACING.md,
  },
});

export default DocumentUploadScreen;
